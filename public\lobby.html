<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moomoo.io Clone - Enter Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        
        .lobby-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .game-title {
            font-size: 3em;
            font-weight: bold;
            color: #2E8B57;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .game-subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 30px;
        }
        
        .input-section {
            margin: 30px 0;
        }
        
        .input-label {
            display: block;
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2E8B57;
        }
        
        .name-input {
            width: 100%;
            max-width: 300px;
            padding: 15px;
            font-size: 1.1em;
            border: 3px solid #2E8B57;
            border-radius: 10px;
            outline: none;
            text-align: center;
            transition: border-color 0.3s;
        }
        
        .name-input:focus {
            border-color: #228B22;
            box-shadow: 0 0 10px rgba(34, 139, 34, 0.3);
        }
        
        .enter-button {
            background: linear-gradient(45deg, #2E8B57, #228B22);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            font-weight: bold;
            border-radius: 10px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .enter-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .enter-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .rules-section {
            margin-top: 30px;
            text-align: left;
            background: rgba(46, 139, 87, 0.1);
            padding: 20px;
            border-radius: 10px;
        }
        
        .rules-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2E8B57;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .rules-list {
            list-style: none;
            padding: 0;
        }
        
        .rules-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .rules-list li:before {
            content: "🎯";
            position: absolute;
            left: 0;
        }
        
        .error-message {
            color: #ff4444;
            font-size: 0.9em;
            margin-top: 5px;
            display: none;
        }
        
        .features {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .feature {
            text-align: center;
            margin: 10px;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 5px;
        }
        
        .feature-text {
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="lobby-container">
        <h1 class="game-title">Moomoo.io</h1>
        <p class="game-subtitle">Multiplayer Survival Game</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🌲</div>
                <div class="feature-text">Gather Resources</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🏗️</div>
                <div class="feature-text">Build Structures</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🐺</div>
                <div class="feature-text">Hunt Animals</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⚔️</div>
                <div class="feature-text">PvP Combat</div>
            </div>
        </div>
        
        <div class="input-section">
            <label class="input-label" for="playerName">Enter Your Name:</label>
            <input 
                type="text" 
                id="playerName" 
                class="name-input" 
                placeholder="Your name (3-20 characters)"
                maxlength="20"
                autocomplete="off"
            >
            <div class="error-message" id="errorMessage">
                Name must be 3-20 characters and contain only letters and numbers
            </div>
        </div>
        
        <button class="enter-button" id="enterGame" disabled>Enter Game</button>
        
        <div class="rules-section">
            <div class="rules-title">Game Rules & Controls</div>
            <ul class="rules-list">
                <li><strong>Movement:</strong> Use WASD or Arrow Keys</li>
                <li><strong>Gather:</strong> Click on trees, rocks, and bushes</li>
                <li><strong>Hunt:</strong> Click on animals for food</li>
                <li><strong>Build:</strong> Use resources to create structures</li>
                <li><strong>Survive:</strong> Avoid wolves and other players</li>
                <li><strong>Grid:</strong> Press G to toggle building grid</li>
            </ul>
        </div>
    </div>
    
    <script>
        const nameInput = document.getElementById('playerName');
        const enterButton = document.getElementById('enterGame');
        const errorMessage = document.getElementById('errorMessage');
        
        // Load saved name if exists
        const savedName = localStorage.getItem('playerName');
        if (savedName) {
            nameInput.value = savedName;
            validateName();
        }
        
        nameInput.addEventListener('input', validateName);
        nameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !enterButton.disabled) {
                enterGame();
            }
        });
        
        enterButton.addEventListener('click', enterGame);
        
        function validateName() {
            const name = nameInput.value.trim();
            const isValid = /^[a-zA-Z0-9]{3,20}$/.test(name);
            
            if (name.length === 0) {
                enterButton.disabled = true;
                errorMessage.style.display = 'none';
            } else if (!isValid) {
                enterButton.disabled = true;
                errorMessage.style.display = 'block';
            } else {
                enterButton.disabled = false;
                errorMessage.style.display = 'none';
            }
        }
        
        function enterGame() {
            const name = nameInput.value.trim();
            if (/^[a-zA-Z0-9]{3,20}$/.test(name)) {
                localStorage.setItem('playerName', name);
                window.location.href = '/game';
            }
        }
    </script>
</body>
</html>
