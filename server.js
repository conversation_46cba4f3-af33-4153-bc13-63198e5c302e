const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Game state
const gameState = {
    players: new Map(),
    resources: new Map(),
    structures: new Map(),
    gameWidth: 2000,
    gameHeight: 2000
};

// Generate initial resources
function generateResources() {
    const resourceTypes = ['tree', 'rock', 'bush'];
    const resourceCount = 100;
    
    for (let i = 0; i < resourceCount; i++) {
        const resource = {
            id: `resource_${i}`,
            type: resourceTypes[Math.floor(Math.random() * resourceTypes.length)],
            x: Math.random() * gameState.gameWidth,
            y: Math.random() * gameState.gameHeight,
            health: 100,
            maxHealth: 100
        };
        gameState.resources.set(resource.id, resource);
    }
}

// Initialize resources
generateResources();

// Player class
class Player {
    constructor(id, x, y) {
        this.id = id;
        this.x = x;
        this.y = y;
        this.health = 100;
        this.maxHealth = 100;
        this.resources = { wood: 0, stone: 0, food: 0 };
        this.weapon = 'hand';
        this.age = 1;
        this.score = 0;
        this.lastUpdate = Date.now();
    }
    
    move(dx, dy) {
        const speed = 3;
        this.x = Math.max(0, Math.min(gameState.gameWidth, this.x + dx * speed));
        this.y = Math.max(0, Math.min(gameState.gameHeight, this.y + dy * speed));
        this.lastUpdate = Date.now();
    }
    
    gatherResource(resourceId) {
        const resource = gameState.resources.get(resourceId);
        if (!resource) return false;
        
        const distance = Math.sqrt((this.x - resource.x) ** 2 + (this.y - resource.y) ** 2);
        if (distance > 50) return false; // Too far to gather
        
        // Damage the resource
        resource.health -= 25;
        
        // Give player resources
        switch (resource.type) {
            case 'tree':
                this.resources.wood += 10;
                break;
            case 'rock':
                this.resources.stone += 10;
                break;
            case 'bush':
                this.resources.food += 10;
                break;
        }
        
        this.score += 5;
        
        // Remove resource if depleted
        if (resource.health <= 0) {
            gameState.resources.delete(resourceId);
        }
        
        return true;
    }
}

// Socket connection handling
io.on('connection', (socket) => {
    console.log('Player connected:', socket.id);
    
    // Create new player
    const player = new Player(
        socket.id,
        Math.random() * gameState.gameWidth,
        Math.random() * gameState.gameHeight
    );
    gameState.players.set(socket.id, player);
    
    // Send initial game state to new player
    socket.emit('gameState', {
        players: Array.from(gameState.players.values()),
        resources: Array.from(gameState.resources.values()),
        structures: Array.from(gameState.structures.values()),
        playerId: socket.id
    });
    
    // Handle player movement
    socket.on('move', (data) => {
        const player = gameState.players.get(socket.id);
        if (player) {
            player.move(data.dx, data.dy);
        }
    });
    
    // Handle resource gathering
    socket.on('gatherResource', (data) => {
        const player = gameState.players.get(socket.id);
        if (player) {
            const success = player.gatherResource(data.resourceId);
            if (success) {
                socket.emit('resourceGathered', {
                    resourceId: data.resourceId,
                    playerResources: player.resources,
                    score: player.score
                });
            }
        }
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
        console.log('Player disconnected:', socket.id);
        gameState.players.delete(socket.id);
    });
});

// Game loop - broadcast game state to all players
setInterval(() => {
    const gameData = {
        players: Array.from(gameState.players.values()),
        resources: Array.from(gameState.resources.values()),
        structures: Array.from(gameState.structures.values())
    };
    
    io.emit('gameUpdate', gameData);
}, 1000 / 30); // 30 FPS

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
