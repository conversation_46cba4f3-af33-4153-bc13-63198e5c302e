const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'lobby.html'));
});

app.get('/game', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Game state
const gameState = {
    players: new Map(),
    resources: new Map(),
    structures: new Map(),
    animals: new Map(),
    gameWidth: 8000,
    gameHeight: 8000
};

// Generate initial resources
function generateResources() {
    const resourceTypes = ['tree', 'rock', 'bush'];
    const resourceCount = 400; // Increased for larger world
    
    for (let i = 0; i < resourceCount; i++) {
        const resource = {
            id: `resource_${i}`,
            type: resourceTypes[Math.floor(Math.random() * resourceTypes.length)],
            x: Math.random() * gameState.gameWidth,
            y: Math.random() * gameState.gameHeight,
            health: 100,
            maxHealth: 100
        };
        gameState.resources.set(resource.id, resource);
    }
}

// Animal class
class Animal {
    constructor(id, type, x, y) {
        this.id = id;
        this.type = type;
        this.x = x;
        this.y = y;
        this.lastUpdate = Date.now();
        this.target = null;
        this.moveDirection = { x: 0, y: 0 };
        this.lastDirectionChange = Date.now();

        // Set properties based on type
        switch (type) {
            case 'rabbit':
                this.health = 15;
                this.maxHealth = 15;
                this.speed = 2;
                this.fleeDistance = 100;
                this.foodValue = 20;
                this.aggressive = false;
                break;
            case 'cow':
                this.health = 40;
                this.maxHealth = 40;
                this.speed = 1;
                this.fleeDistance = 80;
                this.foodValue = 50;
                this.aggressive = false;
                break;
            case 'wolf':
                this.health = 60;
                this.maxHealth = 60;
                this.speed = 2.5;
                this.chaseDistance = 150;
                this.damage = 25;
                this.foodValue = 30;
                this.aggressive = true;
                break;
        }
    }

    update() {
        const now = Date.now();

        if (this.aggressive) {
            this.updateAggressive();
        } else {
            this.updatePassive();
        }

        // Move the animal
        this.x += this.moveDirection.x * this.speed;
        this.y += this.moveDirection.y * this.speed;

        // Keep within world bounds
        this.x = Math.max(50, Math.min(gameState.gameWidth - 50, this.x));
        this.y = Math.max(50, Math.min(gameState.gameHeight - 50, this.y));

        this.lastUpdate = now;
    }

    updatePassive() {
        const now = Date.now();

        // Check for nearby players to flee from
        let shouldFlee = false;
        let fleeDirection = { x: 0, y: 0 };

        for (const player of gameState.players.values()) {
            const distance = Math.sqrt((this.x - player.x) ** 2 + (this.y - player.y) ** 2);
            if (distance < this.fleeDistance) {
                shouldFlee = true;
                const dx = this.x - player.x;
                const dy = this.y - player.y;
                const length = Math.sqrt(dx * dx + dy * dy);
                fleeDirection.x += dx / length;
                fleeDirection.y += dy / length;
            }
        }

        if (shouldFlee) {
            const length = Math.sqrt(fleeDirection.x ** 2 + fleeDirection.y ** 2);
            this.moveDirection.x = fleeDirection.x / length;
            this.moveDirection.y = fleeDirection.y / length;
        } else {
            // Random movement
            if (now - this.lastDirectionChange > 3000) { // Change direction every 3 seconds
                const angle = Math.random() * Math.PI * 2;
                this.moveDirection.x = Math.cos(angle) * 0.3;
                this.moveDirection.y = Math.sin(angle) * 0.3;
                this.lastDirectionChange = now;
            }
        }
    }

    updateAggressive() {
        const now = Date.now();

        // Find nearest player to chase
        let nearestPlayer = null;
        let nearestDistance = Infinity;

        for (const player of gameState.players.values()) {
            const distance = Math.sqrt((this.x - player.x) ** 2 + (this.y - player.y) ** 2);
            if (distance < this.chaseDistance && distance < nearestDistance) {
                nearestPlayer = player;
                nearestDistance = distance;
            }
        }

        if (nearestPlayer) {
            // Chase the player
            const dx = nearestPlayer.x - this.x;
            const dy = nearestPlayer.y - this.y;
            const length = Math.sqrt(dx * dx + dy * dy);
            this.moveDirection.x = dx / length;
            this.moveDirection.y = dy / length;

            // Attack if close enough
            if (nearestDistance < 30) {
                nearestPlayer.health -= this.damage;
                if (nearestPlayer.health <= 0) {
                    nearestPlayer.health = 0;
                    // Player death logic would go here
                }
            }
        } else {
            // Random patrol
            if (now - this.lastDirectionChange > 5000) {
                const angle = Math.random() * Math.PI * 2;
                this.moveDirection.x = Math.cos(angle) * 0.2;
                this.moveDirection.y = Math.sin(angle) * 0.2;
                this.lastDirectionChange = now;
            }
        }
    }
}

// Generate initial animals
function generateAnimals() {
    const animalTypes = [
        { type: 'rabbit', count: 50 },
        { type: 'cow', count: 20 },
        { type: 'wolf', count: 15 }
    ];

    let animalId = 0;
    animalTypes.forEach(({ type, count }) => {
        for (let i = 0; i < count; i++) {
            const animal = new Animal(
                `animal_${animalId++}`,
                type,
                Math.random() * gameState.gameWidth,
                Math.random() * gameState.gameHeight
            );
            gameState.animals.set(animal.id, animal);
        }
    });
}

// Initialize resources and animals
generateResources();
generateAnimals();

// Player class
class Player {
    constructor(id, x, y, name = 'Anonymous') {
        this.id = id;
        this.name = name;
        this.x = x;
        this.y = y;
        this.health = 100;
        this.maxHealth = 100;
        this.resources = { wood: 0, stone: 0, food: 0 };
        this.weapon = 'hand';
        this.age = 1;
        this.score = 0;
        this.lastUpdate = Date.now();
    }
    
    move(dx, dy) {
        const speed = 3;
        const newX = this.x + dx * speed;
        const newY = this.y + dy * speed;

        // World boundaries with buffer
        this.x = Math.max(25, Math.min(gameState.gameWidth - 25, newX));
        this.y = Math.max(25, Math.min(gameState.gameHeight - 25, newY));
        this.lastUpdate = Date.now();
    }
    
    gatherResource(resourceId) {
        const resource = gameState.resources.get(resourceId);
        if (!resource) return false;
        
        const distance = Math.sqrt((this.x - resource.x) ** 2 + (this.y - resource.y) ** 2);
        if (distance > 50) return false; // Too far to gather
        
        // Damage the resource
        resource.health -= 25;
        
        // Give player resources
        switch (resource.type) {
            case 'tree':
                this.resources.wood += 10;
                break;
            case 'rock':
                this.resources.stone += 10;
                break;
            case 'bush':
                this.resources.food += 10;
                break;
        }
        
        this.score += 5;
        
        // Remove resource if depleted
        if (resource.health <= 0) {
            gameState.resources.delete(resourceId);
        }
        
        return true;
    }

    huntAnimal(animalId) {
        const animal = gameState.animals.get(animalId);
        if (!animal) return false;

        const distance = Math.sqrt((this.x - animal.x) ** 2 + (this.y - animal.y) ** 2);
        if (distance > 50) return false; // Too far to hunt

        // Damage the animal
        animal.health -= 30;

        // Give player food and score
        if (animal.health <= 0) {
            this.resources.food += animal.foodValue;
            this.score += animal.foodValue;

            // Remove animal and schedule respawn
            gameState.animals.delete(animalId);
            setTimeout(() => {
                const newAnimal = new Animal(
                    animalId,
                    animal.type,
                    Math.random() * gameState.gameWidth,
                    Math.random() * gameState.gameHeight
                );
                gameState.animals.set(animalId, newAnimal);
            }, 60000); // Respawn after 60 seconds
        }

        return true;
    }
}

// Socket connection handling
io.on('connection', (socket) => {
    console.log('Player connected:', socket.id);

    // Handle player joining with name
    socket.on('joinGame', (data) => {
        const playerName = data.name || 'Anonymous';

        // Create new player
        const player = new Player(
            socket.id,
            Math.random() * gameState.gameWidth,
            Math.random() * gameState.gameHeight,
            playerName
        );
        gameState.players.set(socket.id, player);

        // Send initial game state to new player
        socket.emit('gameState', {
            players: Array.from(gameState.players.values()),
            resources: Array.from(gameState.resources.values()),
            structures: Array.from(gameState.structures.values()),
            animals: Array.from(gameState.animals.values()),
            playerId: socket.id
        });
    });
    
    // Handle player movement
    socket.on('move', (data) => {
        const player = gameState.players.get(socket.id);
        if (player) {
            player.move(data.dx, data.dy);
        }
    });
    
    // Handle resource gathering
    socket.on('gatherResource', (data) => {
        const player = gameState.players.get(socket.id);
        if (player) {
            const success = player.gatherResource(data.resourceId);
            if (success) {
                socket.emit('resourceGathered', {
                    resourceId: data.resourceId,
                    playerResources: player.resources,
                    score: player.score
                });
            }
        }
    });

    // Handle animal hunting
    socket.on('huntAnimal', (data) => {
        const player = gameState.players.get(socket.id);
        if (player) {
            const success = player.huntAnimal(data.animalId);
            if (success) {
                socket.emit('animalHunted', {
                    animalId: data.animalId,
                    playerResources: player.resources,
                    score: player.score
                });
            }
        }
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
        console.log('Player disconnected:', socket.id);
        gameState.players.delete(socket.id);
    });
});

// Game loop - update animals and broadcast game state to all players
setInterval(() => {
    // Update all animals
    for (const animal of gameState.animals.values()) {
        animal.update();
    }

    const gameData = {
        players: Array.from(gameState.players.values()),
        resources: Array.from(gameState.resources.values()),
        structures: Array.from(gameState.structures.values()),
        animals: Array.from(gameState.animals.values())
    };

    io.emit('gameUpdate', gameData);
}, 1000 / 30); // 30 FPS

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
