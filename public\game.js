// Game client
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.minimap = document.getElementById('minimap');
        this.minimapCtx = this.minimap.getContext('2d');
        
        this.socket = io();
        this.playerId = null;
        this.playerName = localStorage.getItem('playerName') || 'Anonymous';
        this.players = [];
        this.resources = [];
        this.structures = [];
        this.animals = [];
        
        this.camera = { x: 0, y: 0, zoom: 1.75 }; // Increased zoom for better visibility
        this.keys = {};
        this.showGrid = true;
        this.gridSize = 50;
        
        this.setupEventListeners();
        this.setupSocketListeners();

        // Join the game with player name
        this.socket.emit('joinGame', { name: this.playerName });

        this.gameLoop();
    }
    
    setupEventListeners() {
        // Keyboard input
        document.addEventListener('keydown', (e) => {
            this.keys[e.key.toLowerCase()] = true;
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.key.toLowerCase()] = false;
        });

        // Grid toggle
        document.addEventListener('keydown', (e) => {
            if (e.key.toLowerCase() === 'g') {
                this.showGrid = !this.showGrid;
            }
        });
        
        // Mouse input for resource gathering
        this.canvas.addEventListener('click', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const mouseX = (e.clientX - rect.left) / this.camera.zoom + this.camera.x;
            const mouseY = (e.clientY - rect.top) / this.camera.zoom + this.camera.y;
            
            // Find closest resource
            let closestResource = null;
            let closestDistance = Infinity;

            this.resources.forEach(resource => {
                const distance = Math.sqrt((mouseX - resource.x) ** 2 + (mouseY - resource.y) ** 2);
                if (distance < closestDistance && distance < 50) {
                    closestDistance = distance;
                    closestResource = resource;
                }
            });

            // Find closest animal
            let closestAnimal = null;
            let closestAnimalDistance = Infinity;

            this.animals.forEach(animal => {
                const distance = Math.sqrt((mouseX - animal.x) ** 2 + (mouseY - animal.y) ** 2);
                if (distance < closestAnimalDistance && distance < 50) {
                    closestAnimalDistance = distance;
                    closestAnimal = animal;
                }
            });

            // Prioritize closer target
            if (closestResource && (!closestAnimal || closestDistance < closestAnimalDistance)) {
                this.socket.emit('gatherResource', { resourceId: closestResource.id });
            } else if (closestAnimal) {
                this.socket.emit('huntAnimal', { animalId: closestAnimal.id });
            }
        });
    }
    
    setupSocketListeners() {
        this.socket.on('gameState', (data) => {
            this.players = data.players;
            this.resources = data.resources;
            this.structures = data.structures;
            this.animals = data.animals || [];
            this.playerId = data.playerId;
        });

        this.socket.on('gameUpdate', (data) => {
            this.players = data.players;
            this.resources = data.resources;
            this.structures = data.structures;
            this.animals = data.animals || [];
        });
        
        this.socket.on('resourceGathered', (data) => {
            // Update UI
            document.getElementById('wood').textContent = data.playerResources.wood;
            document.getElementById('stone').textContent = data.playerResources.stone;
            document.getElementById('food').textContent = data.playerResources.food;
            document.getElementById('score').textContent = data.score;
        });

        this.socket.on('animalHunted', (data) => {
            // Update UI
            document.getElementById('wood').textContent = data.playerResources.wood;
            document.getElementById('stone').textContent = data.playerResources.stone;
            document.getElementById('food').textContent = data.playerResources.food;
            document.getElementById('score').textContent = data.score;
        });
    }
    
    handleInput() {
        let dx = 0, dy = 0;
        
        if (this.keys['w'] || this.keys['arrowup']) dy = -1;
        if (this.keys['s'] || this.keys['arrowdown']) dy = 1;
        if (this.keys['a'] || this.keys['arrowleft']) dx = -1;
        if (this.keys['d'] || this.keys['arrowright']) dx = 1;
        
        if (dx !== 0 || dy !== 0) {
            this.socket.emit('move', { dx, dy });
        }
    }
    
    updateCamera() {
        const player = this.players.find(p => p.id === this.playerId);
        if (player) {
            // Center camera on player, accounting for zoom level
            this.camera.x = player.x - (this.canvas.width / 2) / this.camera.zoom;
            this.camera.y = player.y - (this.canvas.height / 2) / this.camera.zoom;
        }
    }

    snapToGrid(x, y) {
        return {
            x: Math.round(x / this.gridSize) * this.gridSize,
            y: Math.round(y / this.gridSize) * this.gridSize
        };
    }

    drawGrid() {
        if (!this.showGrid) return;

        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
        this.ctx.lineWidth = 1 / this.camera.zoom; // Adjust line width for zoom

        // Calculate visible world area
        const viewWidth = this.canvas.width / this.camera.zoom;
        const viewHeight = this.canvas.height / this.camera.zoom;

        const startX = Math.floor(this.camera.x / this.gridSize) * this.gridSize;
        const startY = Math.floor(this.camera.y / this.gridSize) * this.gridSize;
        const endX = this.camera.x + viewWidth;
        const endY = this.camera.y + viewHeight;

        // Draw vertical lines
        for (let x = startX; x <= endX; x += this.gridSize) {
            const screenX = (x - this.camera.x) * this.camera.zoom;
            if (screenX >= 0 && screenX <= this.canvas.width) {
                this.ctx.beginPath();
                this.ctx.moveTo(screenX, 0);
                this.ctx.lineTo(screenX, this.canvas.height);
                this.ctx.stroke();
            }
        }

        // Draw horizontal lines
        for (let y = startY; y <= endY; y += this.gridSize) {
            const screenY = (y - this.camera.y) * this.camera.zoom;
            if (screenY >= 0 && screenY <= this.canvas.height) {
                this.ctx.beginPath();
                this.ctx.moveTo(0, screenY);
                this.ctx.lineTo(this.canvas.width, screenY);
                this.ctx.stroke();
            }
        }

        this.ctx.restore();
    }

    drawWorldBoundaries() {
        const worldWidth = 8000;
        const worldHeight = 8000;

        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
        this.ctx.lineWidth = 3 / this.camera.zoom; // Adjust line width for zoom

        // Calculate boundary positions relative to camera
        const leftBound = (0 - this.camera.x) * this.camera.zoom;
        const rightBound = (worldWidth - this.camera.x) * this.camera.zoom;
        const topBound = (0 - this.camera.y) * this.camera.zoom;
        const bottomBound = (worldHeight - this.camera.y) * this.camera.zoom;

        const margin = 10 * this.camera.zoom; // Scale margin with zoom

        // Draw boundaries if they're visible
        if (leftBound >= -margin && leftBound <= this.canvas.width + margin) {
            this.ctx.beginPath();
            this.ctx.moveTo(leftBound, 0);
            this.ctx.lineTo(leftBound, this.canvas.height);
            this.ctx.stroke();
        }

        if (rightBound >= -margin && rightBound <= this.canvas.width + margin) {
            this.ctx.beginPath();
            this.ctx.moveTo(rightBound, 0);
            this.ctx.lineTo(rightBound, this.canvas.height);
            this.ctx.stroke();
        }

        if (topBound >= -margin && topBound <= this.canvas.height + margin) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, topBound);
            this.ctx.lineTo(this.canvas.width, topBound);
            this.ctx.stroke();
        }

        if (bottomBound >= -margin && bottomBound <= this.canvas.height + margin) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, bottomBound);
            this.ctx.lineTo(this.canvas.width, bottomBound);
            this.ctx.stroke();
        }

        this.ctx.restore();
    }
    
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#90EE90'; // Light green background
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw grid
        this.drawGrid();

        // Draw world boundaries
        this.drawWorldBoundaries();
        
        // Draw resources
        this.resources.forEach(resource => {
            const x = (resource.x - this.camera.x) * this.camera.zoom;
            const y = (resource.y - this.camera.y) * this.camera.zoom;

            const margin = 50 * this.camera.zoom; // Scale margin with zoom
            if (x > -margin && x < this.canvas.width + margin && y > -margin && y < this.canvas.height + margin) {
                this.ctx.save();
                this.ctx.translate(x, y);
                
                switch (resource.type) {
                    case 'tree':
                        this.ctx.fillStyle = '#8B4513'; // Brown trunk
                        this.ctx.fillRect(-6, -12, 12, 24);
                        this.ctx.fillStyle = '#228B22'; // Green leaves
                        this.ctx.beginPath();
                        this.ctx.arc(0, -18, 18, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                    case 'rock':
                        this.ctx.fillStyle = '#696969'; // Gray rock
                        this.ctx.beginPath();
                        this.ctx.arc(0, 0, 15, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                    case 'bush':
                        this.ctx.fillStyle = '#32CD32'; // Lime green bush
                        this.ctx.beginPath();
                        this.ctx.arc(0, 0, 12, 0, Math.PI * 2);
                        this.ctx.fill();
                        // Add berries
                        this.ctx.fillStyle = '#FF0000';
                        this.ctx.beginPath();
                        this.ctx.arc(-4, -4, 2.5, 0, Math.PI * 2);
                        this.ctx.arc(4, 4, 2.5, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                }
                
                // Health bar
                const healthPercent = resource.health / resource.maxHealth;
                this.ctx.fillStyle = 'red';
                this.ctx.fillRect(-12, -30, 24, 4);
                this.ctx.fillStyle = 'green';
                this.ctx.fillRect(-12, -30, 24 * healthPercent, 4);
                
                this.ctx.restore();
            }
        });

        // Draw animals
        this.animals.forEach(animal => {
            const x = (animal.x - this.camera.x) * this.camera.zoom;
            const y = (animal.y - this.camera.y) * this.camera.zoom;

            const margin = 50 * this.camera.zoom; // Scale margin with zoom
            if (x > -margin && x < this.canvas.width + margin && y > -margin && y < this.canvas.height + margin) {
                this.ctx.save();
                this.ctx.translate(x, y);

                // Draw animal based on type
                switch (animal.type) {
                    case 'rabbit':
                        this.ctx.fillStyle = '#D2B48C'; // Tan
                        this.ctx.beginPath();
                        this.ctx.arc(0, 0, 10, 0, Math.PI * 2);
                        this.ctx.fill();
                        // Ears
                        this.ctx.fillStyle = '#DEB887';
                        this.ctx.beginPath();
                        this.ctx.arc(-4, -10, 4, 0, Math.PI * 2);
                        this.ctx.arc(4, -10, 4, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                    case 'cow':
                        this.ctx.fillStyle = '#FFFFFF'; // White
                        this.ctx.beginPath();
                        this.ctx.arc(0, 0, 15, 0, Math.PI * 2);
                        this.ctx.fill();
                        // Black spots
                        this.ctx.fillStyle = '#000000';
                        this.ctx.beginPath();
                        this.ctx.arc(-5, -5, 3, 0, Math.PI * 2);
                        this.ctx.arc(5, 5, 3, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                    case 'wolf':
                        this.ctx.fillStyle = '#696969'; // Dark gray
                        this.ctx.beginPath();
                        this.ctx.arc(0, 0, 12, 0, Math.PI * 2);
                        this.ctx.fill();
                        // Eyes (red for aggressive)
                        this.ctx.fillStyle = '#FF0000';
                        this.ctx.beginPath();
                        this.ctx.arc(-4, -4, 1.5, 0, Math.PI * 2);
                        this.ctx.arc(4, -4, 1.5, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                }

                // Health bar
                const healthPercent = animal.health / animal.maxHealth;
                this.ctx.fillStyle = 'red';
                this.ctx.fillRect(-12, -25, 24, 4);
                this.ctx.fillStyle = 'green';
                this.ctx.fillRect(-12, -25, 24 * healthPercent, 4);

                this.ctx.restore();
            }
        });

        // Draw players
        this.players.forEach(player => {
            const x = (player.x - this.camera.x) * this.camera.zoom;
            const y = (player.y - this.camera.y) * this.camera.zoom;

            const margin = 50 * this.camera.zoom; // Scale margin with zoom
            if (x > -margin && x < this.canvas.width + margin && y > -margin && y < this.canvas.height + margin) {
                this.ctx.save();
                this.ctx.translate(x, y);
                
                // Player body
                this.ctx.fillStyle = player.id === this.playerId ? '#FF6B6B' : '#4ECDC4';
                this.ctx.beginPath();
                this.ctx.arc(0, 0, 18, 0, Math.PI * 2); // Slightly larger for better visibility
                this.ctx.fill();
                
                // Player name
                this.ctx.fillStyle = 'black';
                this.ctx.font = '12px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(player.name || player.id.substring(0, 6), 0, -25);
                
                // Health bar
                const healthPercent = player.health / player.maxHealth;
                this.ctx.fillStyle = 'red';
                this.ctx.fillRect(-18, -40, 36, 5);
                this.ctx.fillStyle = 'green';
                this.ctx.fillRect(-18, -40, 36 * healthPercent, 5);
                
                this.ctx.restore();
            }
        });
        
        this.renderMinimap();
    }
    
    renderMinimap() {
        const scale = 0.025; // Adjusted for larger world (8000x8000)
        
        // Clear minimap
        this.minimapCtx.fillStyle = '#90EE90';
        this.minimapCtx.fillRect(0, 0, this.minimap.width, this.minimap.height);
        
        // Draw animals on minimap
        this.animals.forEach(animal => {
            const x = animal.x * scale;
            const y = animal.y * scale;

            let color = '#8B4513'; // Brown for passive
            if (animal.type === 'wolf') color = '#FF4500'; // Red for aggressive

            this.minimapCtx.fillStyle = color;
            this.minimapCtx.beginPath();
            this.minimapCtx.arc(x, y, 1, 0, Math.PI * 2);
            this.minimapCtx.fill();
        });

        // Draw players on minimap
        this.players.forEach(player => {
            const x = player.x * scale;
            const y = player.y * scale;

            this.minimapCtx.fillStyle = player.id === this.playerId ? '#FF6B6B' : '#4ECDC4';
            this.minimapCtx.beginPath();
            this.minimapCtx.arc(x, y, 3, 0, Math.PI * 2);
            this.minimapCtx.fill();
        });
        
        // Draw camera view rectangle
        const player = this.players.find(p => p.id === this.playerId);
        if (player) {
            this.minimapCtx.strokeStyle = 'white';
            this.minimapCtx.lineWidth = 2;

            // Calculate actual view area based on zoom level
            const viewWidth = this.canvas.width / this.camera.zoom;
            const viewHeight = this.canvas.height / this.camera.zoom;

            this.minimapCtx.strokeRect(
                this.camera.x * scale,
                this.camera.y * scale,
                viewWidth * scale,
                viewHeight * scale
            );
        }
    }
    
    gameLoop() {
        this.handleInput();
        this.updateCamera();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// Start the game when page loads
window.addEventListener('load', () => {
    new Game();
});
