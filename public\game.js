// Game client
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.minimap = document.getElementById('minimap');
        this.minimapCtx = this.minimap.getContext('2d');
        
        this.socket = io();
        this.playerId = null;
        this.players = [];
        this.resources = [];
        this.structures = [];
        
        this.camera = { x: 0, y: 0 };
        this.keys = {};
        
        this.setupEventListeners();
        this.setupSocketListeners();
        this.gameLoop();
    }
    
    setupEventListeners() {
        // Keyboard input
        document.addEventListener('keydown', (e) => {
            this.keys[e.key.toLowerCase()] = true;
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.key.toLowerCase()] = false;
        });
        
        // Mouse input for resource gathering
        this.canvas.addEventListener('click', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left + this.camera.x;
            const mouseY = e.clientY - rect.top + this.camera.y;
            
            // Find closest resource
            let closestResource = null;
            let closestDistance = Infinity;
            
            this.resources.forEach(resource => {
                const distance = Math.sqrt((mouseX - resource.x) ** 2 + (mouseY - resource.y) ** 2);
                if (distance < closestDistance && distance < 50) {
                    closestDistance = distance;
                    closestResource = resource;
                }
            });
            
            if (closestResource) {
                this.socket.emit('gatherResource', { resourceId: closestResource.id });
            }
        });
    }
    
    setupSocketListeners() {
        this.socket.on('gameState', (data) => {
            this.players = data.players;
            this.resources = data.resources;
            this.structures = data.structures;
            this.playerId = data.playerId;
        });
        
        this.socket.on('gameUpdate', (data) => {
            this.players = data.players;
            this.resources = data.resources;
            this.structures = data.structures;
        });
        
        this.socket.on('resourceGathered', (data) => {
            // Update UI
            document.getElementById('wood').textContent = data.playerResources.wood;
            document.getElementById('stone').textContent = data.playerResources.stone;
            document.getElementById('food').textContent = data.playerResources.food;
            document.getElementById('score').textContent = data.score;
        });
    }
    
    handleInput() {
        let dx = 0, dy = 0;
        
        if (this.keys['w'] || this.keys['arrowup']) dy = -1;
        if (this.keys['s'] || this.keys['arrowdown']) dy = 1;
        if (this.keys['a'] || this.keys['arrowleft']) dx = -1;
        if (this.keys['d'] || this.keys['arrowright']) dx = 1;
        
        if (dx !== 0 || dy !== 0) {
            this.socket.emit('move', { dx, dy });
        }
    }
    
    updateCamera() {
        const player = this.players.find(p => p.id === this.playerId);
        if (player) {
            this.camera.x = player.x - this.canvas.width / 2;
            this.camera.y = player.y - this.canvas.height / 2;
        }
    }
    
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#90EE90'; // Light green background
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw resources
        this.resources.forEach(resource => {
            const x = resource.x - this.camera.x;
            const y = resource.y - this.camera.y;
            
            if (x > -50 && x < this.canvas.width + 50 && y > -50 && y < this.canvas.height + 50) {
                this.ctx.save();
                this.ctx.translate(x, y);
                
                switch (resource.type) {
                    case 'tree':
                        this.ctx.fillStyle = '#8B4513'; // Brown trunk
                        this.ctx.fillRect(-5, -10, 10, 20);
                        this.ctx.fillStyle = '#228B22'; // Green leaves
                        this.ctx.beginPath();
                        this.ctx.arc(0, -15, 15, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                    case 'rock':
                        this.ctx.fillStyle = '#696969'; // Gray rock
                        this.ctx.beginPath();
                        this.ctx.arc(0, 0, 12, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                    case 'bush':
                        this.ctx.fillStyle = '#32CD32'; // Lime green bush
                        this.ctx.beginPath();
                        this.ctx.arc(0, 0, 10, 0, Math.PI * 2);
                        this.ctx.fill();
                        // Add berries
                        this.ctx.fillStyle = '#FF0000';
                        this.ctx.beginPath();
                        this.ctx.arc(-3, -3, 2, 0, Math.PI * 2);
                        this.ctx.arc(3, 3, 2, 0, Math.PI * 2);
                        this.ctx.fill();
                        break;
                }
                
                // Health bar
                const healthPercent = resource.health / resource.maxHealth;
                this.ctx.fillStyle = 'red';
                this.ctx.fillRect(-10, -25, 20, 3);
                this.ctx.fillStyle = 'green';
                this.ctx.fillRect(-10, -25, 20 * healthPercent, 3);
                
                this.ctx.restore();
            }
        });
        
        // Draw players
        this.players.forEach(player => {
            const x = player.x - this.camera.x;
            const y = player.y - this.camera.y;
            
            if (x > -50 && x < this.canvas.width + 50 && y > -50 && y < this.canvas.height + 50) {
                this.ctx.save();
                this.ctx.translate(x, y);
                
                // Player body
                this.ctx.fillStyle = player.id === this.playerId ? '#FF6B6B' : '#4ECDC4';
                this.ctx.beginPath();
                this.ctx.arc(0, 0, 15, 0, Math.PI * 2);
                this.ctx.fill();
                
                // Player name/ID (simplified)
                this.ctx.fillStyle = 'black';
                this.ctx.font = '12px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(player.id.substring(0, 6), 0, -25);
                
                // Health bar
                const healthPercent = player.health / player.maxHealth;
                this.ctx.fillStyle = 'red';
                this.ctx.fillRect(-15, -35, 30, 4);
                this.ctx.fillStyle = 'green';
                this.ctx.fillRect(-15, -35, 30 * healthPercent, 4);
                
                this.ctx.restore();
            }
        });
        
        this.renderMinimap();
    }
    
    renderMinimap() {
        const scale = 0.1;
        
        // Clear minimap
        this.minimapCtx.fillStyle = '#90EE90';
        this.minimapCtx.fillRect(0, 0, this.minimap.width, this.minimap.height);
        
        // Draw players on minimap
        this.players.forEach(player => {
            const x = player.x * scale;
            const y = player.y * scale;
            
            this.minimapCtx.fillStyle = player.id === this.playerId ? '#FF6B6B' : '#4ECDC4';
            this.minimapCtx.beginPath();
            this.minimapCtx.arc(x, y, 3, 0, Math.PI * 2);
            this.minimapCtx.fill();
        });
        
        // Draw camera view rectangle
        const player = this.players.find(p => p.id === this.playerId);
        if (player) {
            this.minimapCtx.strokeStyle = 'white';
            this.minimapCtx.lineWidth = 2;
            this.minimapCtx.strokeRect(
                this.camera.x * scale,
                this.camera.y * scale,
                this.canvas.width * scale,
                this.canvas.height * scale
            );
        }
    }
    
    gameLoop() {
        this.handleInput();
        this.updateCamera();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// Start the game when page loads
window.addEventListener('load', () => {
    new Game();
});
