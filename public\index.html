<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moomoo.io Clone</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #87CEEB;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #gameCanvas {
            border: 2px solid #333;
            cursor: crosshair;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        #resources {
            margin-top: 10px;
        }
        
        .resource-item {
            margin: 2px 0;
        }
        
        #minimap {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 200px;
            height: 200px;
            background-color: rgba(0, 0, 0, 0.7);
            border: 2px solid #333;
        }
        
        #instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    
    <div id="ui">
        <div>Health: <span id="health">100</span>/100</div>
        <div>Score: <span id="score">0</span></div>
        <div>Age: <span id="age">1</span></div>
        <div id="resources">
            <div class="resource-item">🪵 Wood: <span id="wood">0</span></div>
            <div class="resource-item">🪨 Stone: <span id="stone">0</span></div>
            <div class="resource-item">🍎 Food: <span id="food">0</span></div>
        </div>
    </div>
    
    <canvas id="minimap" width="200" height="200"></canvas>
    
    <div id="instructions">
        <div><strong>Controls:</strong></div>
        <div>WASD or Arrow Keys: Move</div>
        <div>Click: Gather resources</div>
        <div>G: Toggle grid overlay</div>
        <div>Space: Attack (coming soon)</div>
    </div>
    
    <script src="/socket.io/socket.io.js"></script>
    <script src="game.js"></script>
</body>
</html>
